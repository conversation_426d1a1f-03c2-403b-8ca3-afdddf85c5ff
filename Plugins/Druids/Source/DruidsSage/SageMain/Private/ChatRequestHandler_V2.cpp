#include "ChatRequestHandler_V2.h"
#include "DruidsSageMessagingHandler.h"
#include "DruidsSageHelper.h"
#include "IDruidsSageChatItem.h"
#include "SageExtensionDelegator.h"
#include "DruidsSageChatLogger.h"
#include "Misc/FileHelper.h"

bool ChatRequestHandler_V2::IsNoActiveRequest() const
{
    return !RequestReference.IsValid() || !UDruidsSageTaskStatus::IsTaskActive(RequestReference.Get());
}

void ChatRequestHandler_V2::StopAndCleanupRequest(TArray<UIDruidsSageChatItem*> ChatItems)
{
    if (RequestReference.IsValid())
    {
        // Find the last assistant message to get its messaging handler
        for (UIDruidsSageChatItem* ChatItem : ChatItems)
        {
            if (ChatItem)
            {
                if (ChatItem->GetMessageRole() == EDruidsSageChatRole::Assistant && ChatItem->GetMessagingHandler().IsValid())
                {
                    // Unbind all delegates
                    ChatItem->GetMessagingHandler()->OnMessageRequestSent.Unbind();
                    ChatItem->GetMessagingHandler()->OnMessageRequestFailed.Unbind();
                    ChatItem->GetMessagingHandler()->OnMessageContentUpdated.Unbind();
                    ChatItem->GetMessagingHandler()->OnMessageResponseUpdated.Unbind();

                    RequestReference->ProgressStarted.RemoveAll(ChatItem->GetMessagingHandler().Get());
                    RequestReference->ProgressUpdated.RemoveAll(ChatItem->GetMessagingHandler().Get());
                    RequestReference->ProcessCompleted.RemoveAll(ChatItem->GetMessagingHandler().Get());
                    RequestReference->ErrorReceived.RemoveAll(ChatItem->GetMessagingHandler().Get());
                    RequestReference->RequestFailed.RemoveAll(ChatItem->GetMessagingHandler().Get());
                    RequestReference->RequestSent.RemoveAll(ChatItem->GetMessagingHandler().Get());
                }
            }
        }
        
        RequestReference->StopDruidsSageTask();
        RequestReference.Reset();
    }
}

void ChatRequestHandler_V2::SetupAndSendRequest(TArray<UIDruidsSageChatItem*> ChatItems,
                                                 UIDruidsSageChatItem* AssistantMessage, const FString& Context,
                                                 const bool UseExtensions, const bool UseInvocations,
                                                 const FString& ThreadId)
{
    if (!AssistantMessage || !AssistantMessage->GetMessagingHandler().IsValid())
    {
        return;
    }

    TArray<FDruidsSageChatMessage> ChatHistory;

    // If we have a thread ID, only send the most recent user message
    // Otherwise, send the full conversation history
    if (!ThreadId.IsEmpty())
    {
        // Find the most recent user message
        for (int32 i = ChatItems.Num() - 1; i >= 0; i--)
        {
            UIDruidsSageChatItem* Item = ChatItems[i];
            if (Item && Item->GetMessageRole() == EDruidsSageChatRole::User)
            {
                FDruidsSageChatMessage ChatMessage;
                Item->FillInDruidsMessage(ChatMessage);

                // Add UserFocusContext to the user message
                if (!Context.IsEmpty())
                {
                    ChatMessage.SetUserFocusContext(Context);
                }

                ChatHistory.Add(ChatMessage);
                break; // Only add the most recent user message
            }
        }
    }
    else
    {
        // Send full conversation history when no thread ID
	for (UIDruidsSageChatItem* Item : ChatItems)
    {
        if (Item)
        {
            FString MessageRoleText = UDruidsSageHelper::RoleToName(Item->GetMessageRole()).ToString();

	        FDruidsSageChatMessage ChatMessage;
	        Item->FillInDruidsMessage(ChatMessage);

            // Add UserFocusContext to user messages
            if (Item->GetMessageRole() == EDruidsSageChatRole::User && !Context.IsEmpty())
            {
                ChatMessage.SetUserFocusContext(Context);
            }

            // Add chat history to user messages (only for the most recent user message)
            if (Item->GetMessageRole() == EDruidsSageChatRole::User)
            {
                // Check if this is the last user message in the chat items
                bool bIsLastUserMessage = true;
                for (int32 i = ChatItems.Num() - 1; i >= 0; i--)
                {
                    if (ChatItems[i] == Item)
                    {
                        // Found current item, check if there are any user messages after it
                        for (int32 j = i + 1; j < ChatItems.Num(); j++)
                        {
                            if (ChatItems[j] && ChatItems[j]->GetMessageRole() == EDruidsSageChatRole::User)
                            {
                                bIsLastUserMessage = false;
                                break;
                            }
                        }
                        break;
                    }
                }

                // Only add chat history to the most recent user message
                if (bIsLastUserMessage)
                {
                        // Get the chat history from the logger and format it as markdown
                    FString ChatHistoryPath = DruidsSageChatLogger::GetChatLogPath();
                        FString ChatHistoryContent;
                        if (FFileHelper::LoadFileToString(ChatHistoryContent, *ChatHistoryPath))
                        {
                            if (!ChatHistoryContent.IsEmpty())
                            {
                                ChatMessage.SetChatMemories(ChatHistoryContent);
                            }
                        }
                    }
                }

            ChatHistory.Add(ChatMessage);
        }
    }
    }

    ExtensionDelegator = MakeShared<FSageExtensionDelegator>();
    RequestReference = UDruidsSageChatRequest_v2::EditorTask(ChatHistory, ExtensionDelegator, Context,
                                                             UseExtensions, UseInvocations, ThreadId);

    RequestReference->ProgressStarted.AddDynamic(
        AssistantMessage->GetMessagingHandler().Get(),
        &UDruidsSageMessagingHandler::ProcessUpdated);

    RequestReference->ProgressUpdated.AddDynamic(
        AssistantMessage->GetMessagingHandler().Get(),
        &UDruidsSageMessagingHandler::ProcessUpdated);

    RequestReference->ProcessCompleted.AddDynamic(
        AssistantMessage->GetMessagingHandler().Get(),
        &UDruidsSageMessagingHandler::ProcessCompleted);

    RequestReference->ErrorReceived.AddDynamic(
        AssistantMessage->GetMessagingHandler().Get(),
        &UDruidsSageMessagingHandler::ProcessCompleted);

    RequestReference->RequestFailed.AddDynamic(
        AssistantMessage->GetMessagingHandler().Get(),
        &UDruidsSageMessagingHandler::RequestFailed);

    RequestReference->RequestSent.AddDynamic(
        AssistantMessage->GetMessagingHandler().Get(),
        &UDruidsSageMessagingHandler::RequestSent);

    // Bind to thread ID received delegate
    RequestReference->ThreadIdReceived.AddLambda([this](const FString& ReceivedThreadId)
    {
        if (ThreadIdCallback.IsBound())
        {
            ThreadIdCallback.Execute(ReceivedThreadId);
        }
    });

    RequestReference->Activate();
}

void ChatRequestHandler_V2::SetThreadIdCallback(const FOnThreadIdReceived& Callback)
{
    ThreadIdCallback = Callback;
}