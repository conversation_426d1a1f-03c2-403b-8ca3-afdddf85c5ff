#include "DruidsSageChatTypes.h"

#include <Dom/JsonObject.h>

#include "DruidsSageHelper.h"

#include "SageExtensionTypes.h"

#include "DruidsSageSettings.h"

#ifdef UE_INLINE_GENERATED_CPP_BY_NAME
#include UE_INLINE_GENERATED_CPP_BY_NAME(DruidsSageChatTypes)
#endif

FDruidsSageChatMessage::FDruidsSageChatMessage(const FName& InRole, const FString& InChatContent,
                                               const FString& InDisplayContent,
                                               const TArray<TSharedPtr<FDruidsSageExtensionDefinition>>& InActiveExtensionDefinitions)
{
	Role = UDruidsSageHelper::NameToRole(InRole);
	ChatContent = InChatContent;
	ActiveExtensionDefinitions = InActiveExtensionDefinitions;
}

TSharedPtr<FJsonValue> FDruidsSageChatMessage::GetMessageJson(bool bIncludeExtensions) const
{
	TSharedPtr<FJsonObject> JsonObject = MakeShared<FJsonObject>();
	JsonObject->SetStringField("role", UDruidsSageHelper::RoleToName(Role).ToString().ToLower());

	if (!ContentArray.IsEmpty())
	{
		JsonObject->SetArrayField("content", ContentArray);
	}
	else
	{
		TArray<TSharedPtr<FJsonObject>> ContentArrayReturn;

		// Add text content
		TSharedPtr<FJsonObject> TextContent = MakeShared<FJsonObject>();
		TextContent->SetStringField("type", "text");
		TextContent->SetStringField("text", ChatContent);
		ContentArrayReturn.Add(TextContent);

		// Add UserFocusContext as a content item if it's not empty
		if (!UserFocusContext.IsEmpty())
		{
			TSharedPtr<FJsonObject> FocusContent = MakeShared<FJsonObject>();
			FocusContent->SetStringField("type", "user_focus_context");
			FocusContent->SetStringField("user_focus_context", UserFocusContext);
			ContentArrayReturn.Add(FocusContent);
		}

		// Add UserChatHistory as a content item if it's not empty
		if (!UserChatHistory.IsEmpty())
		{
			TSharedPtr<FJsonObject> ChatHistoryContent = MakeShared<FJsonObject>();
			ChatHistoryContent->SetStringField("type", "chat_memories");
			ChatHistoryContent->SetStringField("chat_memories", UserChatHistory);
			ContentArrayReturn.Add(ChatHistoryContent);
		}

		if (bIncludeExtensions)
		{
			// Add extension content for each active extension
			for (const TSharedPtr<FDruidsSageExtensionDefinition> Extension : ActiveExtensionDefinitions)
			{
				if (Extension.Get())
				{
					TSharedPtr<FJsonObject> ExtensionContent = MakeShared<FJsonObject>();
					ExtensionContent->SetStringField("type", "extension");
					ExtensionContent->SetObjectField("extension", Extension.Get()->GetExtensionDefinitionJson());
					ContentArrayReturn.Add(ExtensionContent);
				}
			}
		}

		// Convert array to JsonValue array and set it in the parent object
		TArray<TSharedPtr<FJsonValue>> JsonContentArray;
		for (const TSharedPtr<FJsonObject>& Content : ContentArrayReturn)
		{
			JsonContentArray.Add(MakeShared<FJsonValueObject>(Content));
		}
		JsonObject->SetArrayField("content", JsonContentArray);
	}

	// Add thinking array if it's not empty
	if (!ThinkingArray.IsEmpty())
	{
		JsonObject->SetArrayField("thinking", ThinkingArray);
	}

	return MakeShared<FJsonValueObject>(JsonObject);
}

void FDruidsSageChatMessage::SetRole(const EDruidsSageChatRole& NewRole)
{
	Role = NewRole;
}

EDruidsSageChatRole FDruidsSageChatMessage::GetRole() const
{
	return Role;
}

void FDruidsSageChatMessage::SetContentArray(const TArray<TSharedPtr<FJsonValue>>& NewContentArray)
{
	ContentArray = NewContentArray;
}

TArray<TSharedPtr<FJsonValue>> FDruidsSageChatMessage::GetContentArray() const
{
	return ContentArray;
}

void FDruidsSageChatMessage::SetChatContent(const FString& NewContent)
{
	ChatContent = NewContent;
}

FString FDruidsSageChatMessage::GetChatContent() const
{
	if (ChatContent.IsEmpty() && ContentArray.Num() > 0)
	{
		FString returnString;
		for (const TSharedPtr<FJsonValue>& Content : ContentArray)
		{
			if (Content.IsValid())
			{
				TSharedPtr<FJsonObject> JsonObject = Content->AsObject();
				if (!JsonObject.IsValid())
				{
					continue;
				}

				if (FString ContentType; JsonObject->TryGetStringField(TEXT("type"), ContentType))
				{
					if (ContentType == "text")
					{
						if (FString Text; JsonObject->TryGetStringField(TEXT("text"), Text))
						{
							if (returnString.Len() > 0)
							{
								returnString += "\n";
							}
							
							returnString += Text;
						}
					}
					else if (ContentType == "action_request")
					{
						if (const TSharedPtr<FJsonObject>* ActionRequest;
							JsonObject->TryGetObjectField(TEXT("action_request"), ActionRequest))
						{
							if (FString ActionSummary;
								ActionRequest->Get()->TryGetStringField(TEXT("summary"), ActionSummary))
							{
								if (returnString.Len() > 0)
								{
									returnString += "\n\n";
								}

								returnString += TEXT("  **User Action Request**:") + ActionSummary;
								returnString += "\n\n    **APPLY**\n\n";
							}							
						}
					}
				}
			}
		}

		return returnString;
	}
	
	return ChatContent;
}

void FDruidsSageChatMessage::SetActiveExtensionDefinitions(
	const TArray<TSharedPtr<FDruidsSageExtensionDefinition>>& NewActiveExtensionDefintions)
{
	ActiveExtensionDefinitions = NewActiveExtensionDefintions;
}

TArray<TSharedPtr<FDruidsSageExtensionDefinition>> FDruidsSageChatMessage::GetActiveExtensionDefinitions() const
{
	return ActiveExtensionDefinitions;
}

void FDruidsSageChatMessage::SetUserFocusContext(const FString& NewUserFocusContext)
{
    UserFocusContext = NewUserFocusContext;
}

FString FDruidsSageChatMessage::GetUserFocusContext() const
{
    return UserFocusContext;
}

void FDruidsSageChatMessage::SetUserChatHistory(const FString& NewUserChatHistory)
{
    UserChatHistory = NewUserChatHistory;
}

FString FDruidsSageChatMessage::GetUserChatHistory() const
{
    return UserChatHistory;
}

void FDruidsSageChatMessage::SetThinkingArray(const TArray<TSharedPtr<FJsonValue>>& NewThinkingArray)
{
	ThinkingArray = NewThinkingArray;
}

TArray<TSharedPtr<FJsonValue>> FDruidsSageChatMessage::GetThinkingArray() const
{
	return ThinkingArray;
}
