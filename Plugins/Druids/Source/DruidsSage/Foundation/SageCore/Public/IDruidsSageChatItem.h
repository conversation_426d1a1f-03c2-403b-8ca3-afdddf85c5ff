#pragma once

#include <CoreMinimal.h>
#include <Blueprint/UserWidget.h>

#include "DruidsSageChatTypes.h"

#include "IDruidsSageChatItem.generated.h"

class UDruidsSageMessagingHandler;
struct FDruidsSageExtensionDefinition;

UCLASS(Abstract, BlueprintType, Blueprintable)
class SAGECORE_API UIDruidsSageChatItem : public UUserWidget
{
    GENERATED_BODY()

public:
    // C++ virtual methods for implementation
    virtual FName GetTypeName() const PURE_VIRTUAL(UIDruidsSageChatItem::GetTypeNameCpp, return NAME_None;);
    virtual void FillInDruidsMessage(FDruidsSageChatMessage& Message) const PURE_VIRTUAL(UIDruidsSageChatItem::FillInDruidsMessageCpp, );
    virtual EDruidsSageChatRole GetMessageRole() const PURE_VIRTUAL(UIDruidsSageChatItem::GetMessageRoleCpp, return EDruidsSageChatRole::User;);
    virtual TWeakObjectPtr<UDruidsSageMessagingHandler> GetMessagingHandler() const PURE_VIRTUAL(UIDruidsSageChatItem::GetMessagingHandlerCpp, return nullptr;);
    virtual void UpdateFromContentJson(const TSharedPtr<FJsonValue>& ContentJson) PURE_VIRTUAL(UIDruidsSageChatItem::UpdateFromContentJsonCpp, );

    // Get plain text representation of this chat item
    virtual FString GetPlainText() const PURE_VIRTUAL(UIDruidsSageChatItem::GetPlainTextCpp, return FString(););
};
