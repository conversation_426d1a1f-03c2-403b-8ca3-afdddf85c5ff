// These structures are defined in the common module due to being used in both modules, to avoid cyclic dependencies.

#pragma once

#include <CoreMinimal.h>
#include <Dom/JsonValue.h>

#include "DruidsSageCommonTypes.h"
#include "DruidsSageChatTypes.generated.h"

struct FDruidsSageExtensionDefinition;

UENUM(BlueprintType, Category = "DruidsSage | Chat", Meta = (DisplayName = "DruidsSage Chat Role"))
enum class EDruidsSageChatRole : uint8
{
	User,
	Assistant,
	System,
};

USTRUCT(BlueprintType, Category = "DruidsSage | Chat", Meta = (DisplayName = "DruidsSage Chat Message"))
struct SAGECOMMONTYPES_API FDruidsSageChatMessage
{
	GENERATED_BODY()

	FDruidsSageChatMessage() = default;

	FDruidsSageChatMessage(const FName& InRole, const FString& InChatContent, const FString& InDisplayContent,
	                       const TArray<TSharedPtr<FDruidsSageExtensionDefinition>>& InActiveExtensionDefinitions);

	TSharedPtr<FJsonValue> GetMessageJson(bool bIncludeExtensions) const;

	void SetRole(const EDruidsSageChatRole& NewRole);
	EDruidsSageChatRole GetRole() const;

	void SetContentArray(const TArray<TSharedPtr<FJsonValue>>& NewContentArray);
	TArray<TSharedPtr<FJsonValue>> GetContentArray() const;
	
	void SetChatContent(const FString& NewContent);
	FString GetChatContent() const;
	
	void SetActiveExtensionDefinitions(const TArray<TSharedPtr<FDruidsSageExtensionDefinition>>& NewActiveExtensionDefintions);
	TArray<TSharedPtr<FDruidsSageExtensionDefinition>> GetActiveExtensionDefinitions() const;

	void SetUserFocusContext(const FString& NewUserFocusContext);
	FString GetUserFocusContext() const;

	void SetChatMemories(const FString& NewChatMemories);
	FString GetUserChatHistory() const;

	void SetThinkingArray(const TArray<TSharedPtr<FJsonValue>>& NewThinkingArray);
	TArray<TSharedPtr<FJsonValue>> GetThinkingArray() const;

protected:
	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Chat")
	EDruidsSageChatRole Role = EDruidsSageChatRole::User;

	TArray<TSharedPtr<FJsonValue>> ContentArray;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Chat")
	FString ChatContent;

	TArray<TSharedPtr<FDruidsSageExtensionDefinition>> ActiveExtensionDefinitions;

private:
	FString UserFocusContext;
	FString ChatMemories;
	TArray<TSharedPtr<FJsonValue>> ThinkingArray;
};

USTRUCT(BlueprintType, Category = "DruidsSage | Chat", Meta = (DisplayName = "DruidsSage Chat Choice"))
struct SAGECOMMONTYPES_API FDruidsSageChatChoice
{
	GENERATED_BODY()

	FDruidsSageChatChoice() = default;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Chat")
	int32 Index = 0;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Chat")
	FDruidsSageChatMessage Message;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Chat")
	FName FinishReason = NAME_None;
};

USTRUCT(BlueprintType, Category = "DruidsSage | Chat", Meta = (DisplayName = "DruidsSage Chat Usage"))
struct SAGECOMMONTYPES_API FDruidsSageChatUsage
{
	GENERATED_BODY()

	FDruidsSageChatUsage() = default;

	FDruidsSageChatUsage(const int32& PromptTokens, const int32& CompletionTokens, const int32& TotalTokens) : PromptTokens(PromptTokens),
		CompletionTokens(CompletionTokens), TotalTokens(TotalTokens)
	{
	}

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Chat")
	int32 PromptTokens = 0;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Chat")
	int32 CompletionTokens = 0;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Chat")
	int32 TotalTokens = 0;
};

USTRUCT(BlueprintType, Category = "DruidsSage | Chat", Meta = (DisplayName = "DruidsSage Chat Response"))
struct SAGECOMMONTYPES_API FDruidsSageChatResponse
{
	GENERATED_BODY()

	FDruidsSageChatResponse() = default;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Chat")
	FName ID = NAME_None;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Chat")
	FName Object = NAME_None;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Chat")
	int32 Created = 0;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Chat")
	TArray<FDruidsSageChatChoice> Choices;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Chat")
	FDruidsSageChatUsage Usage;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Chat")
	bool bSuccess = false;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Chat")
	FDruidsSageCommonError Error;
};
