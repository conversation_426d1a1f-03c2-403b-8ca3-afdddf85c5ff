#pragma once

#include <CoreMinimal.h>

/**
 * Structure to hold a single chat exchange
 */
struct SAGECORE_API FChatLogEntry
{
	FString UserRequest;
	FString AssistantResponse;
	FDateTime Timestamp;

	FChatLogEntry()
		: Timestamp(FDateTime::Now())
	{
	}

	FChatLogEntry(const FString& InUserRequest, const FString& InAssistantResponse)
		: UserRequest(InUserRequest)
		, AssistantResponse(InAssistantResponse)
		, Timestamp(FDateTime::Now())
	{
	}
};

/**
 * Handles logging of chat conversations to a markdown file
 * Maintains the last 100 chat exchanges in /Saved/DruidsSage/ChatLog.md
 */
class SAGECORE_API DruidsSageChatLogger
{
public:
	/**
	 * Log a completed chat exchange (user request + assistant response)
	 * @param UserRequest The plain text of the user's request
	 * @param AssistantResponse The plain text of the assistant's response
	 */
	static void LogChatExchange(const FString& UserRequest, const FString& AssistantResponse);

	/**
	 * Load existing chat log entries from file
	 * @return Array of chat log entries loaded from file
	 */
	static TArray<FChatLogEntry> LoadChatLog();

	/**
	 * Save chat log entries to file
	 * @param ChatLogEntries Array of chat log entries to save
	 */
	static void SaveChatLog(const TArray<FChatLogEntry>& ChatLogEntries);

	/**
	 * Get the path to the chat log file
	 */
	static FString GetChatLogPath();

private:
	/**
	 * Ensure the chat log directory exists
	 */
	static void EnsureLogDirectoryExists();

	/**
	 * Trim the log to maintain only the last 100 entries
	 * @param ChatLogEntries Array to trim (modified in place)
	 */
	static void TrimLogToMaxEntries(TArray<FChatLogEntry>& ChatLogEntries);

	/**
	 * Maximum number of entries to keep in the log
	 */
	static constexpr int32 MaxLogEntries = 100;
};
