#include "DruidsSageChatMemories.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Misc/DateTime.h"

void DruidsSageChatMemories::LogChatExchange(const FString& UserRequest, const FString& AssistantResponse)
{
	// Skip empty exchanges
	if (UserRequest.IsEmpty() && AssistantResponse.IsEmpty())
	{
		return;
	}

	// Load existing entries
	TArray<FChatMemoryEntry> ChatLogEntries = LoadChatMemories();

	// Add new entry
	FChatMemoryEntry NewEntry(UserRequest, AssistantResponse);
	ChatLogEntries.Add(NewEntry);

	// Trim to max entries
	TrimMemoriesToMaxEntries(ChatLogEntries);

	// Save to file
	SaveChatMemories(ChatLogEntries);
}

FString DruidsSageChatMemories::GetChatLogPath()
{
	return FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("DruidsSage"), TEXT("ChatLog.md"));
}

TArray<FChatMemoryEntry> DruidsSageChatMemories::LoadChatMemories()
{
	TArray<FChatMemoryEntry> ChatLogEntries;
	FString ChatLogPath = GetChatLogPath();
	FString LogContent;

	if (FFileHelper::LoadFileToString(LogContent, *ChatLogPath))
	{
		// Parse the markdown content to extract entries
		TArray<FString> Lines;
		LogContent.ParseIntoArrayLines(Lines);

		FString CurrentUserRequest;
		FString CurrentAssistantResponse;
		bool bParsingUser = false;
		bool bParsingAssistant = false;

		for (const FString& Line : Lines)
		{
			if (Line.StartsWith(TEXT("### Request")))
			{
				// Save previous entry if we have one
				if (!CurrentUserRequest.IsEmpty() || !CurrentAssistantResponse.IsEmpty())
				{
					ChatLogEntries.Add(FChatMemoryEntry(CurrentUserRequest, CurrentAssistantResponse));
				}

				// Reset for new entry
				CurrentUserRequest.Empty();
				CurrentAssistantResponse.Empty();
				bParsingUser = false;
				bParsingAssistant = false;
			}
			else if (Line.StartsWith(TEXT("**User**:")))
			{
				bParsingUser = true;
				bParsingAssistant = false;
				// Extract user request (remove "**User**: " prefix)
				CurrentUserRequest = Line.Mid(9); // "**User**: " is 9 characters
			}
			else if (Line.StartsWith(TEXT("**Assistant**:")))
			{
				bParsingUser = false;
				bParsingAssistant = true;
				// Extract assistant response (remove "**Assistant**: " prefix)
				CurrentAssistantResponse = Line.Mid(14); // "**Assistant**: " is 14 characters
			}
			else if (bParsingUser && !Line.IsEmpty())
			{
				// Continue user request on new line
				if (!CurrentUserRequest.IsEmpty())
				{
					CurrentUserRequest += TEXT("\n");
				}
				CurrentUserRequest += Line;
			}
			else if (bParsingAssistant && !Line.IsEmpty())
			{
				// Continue assistant response on new line
				if (!CurrentAssistantResponse.IsEmpty())
				{
					CurrentAssistantResponse += TEXT("\n");
				}
				CurrentAssistantResponse += Line;
			}
		}

		// Save the last entry
		if (!CurrentUserRequest.IsEmpty() || !CurrentAssistantResponse.IsEmpty())
		{
			ChatLogEntries.Add(FChatMemoryEntry(CurrentUserRequest, CurrentAssistantResponse));
		}

		// Trim to max entries in case the file had more
		TrimMemoriesToMaxEntries(ChatLogEntries);
	}

	return ChatLogEntries;
}

void DruidsSageChatMemories::SaveChatMemories(const TArray<FChatMemoryEntry>& ChatMemoryEntries)
{
	EnsureMemoriesDirectoryExists();

	FString ChatLogPath = GetChatLogPath();
	FString LogContent;

	// Build markdown content
	for (const FChatMemoryEntry& Entry : ChatMemoryEntries)
	{
		LogContent += TEXT("### Request\n");
		LogContent += FString::Printf(TEXT("**User**: %s\n"), *Entry.UserRequest);
		LogContent += FString::Printf(TEXT("**Assistant**: %s\n"), *Entry.AssistantResponse);
		LogContent += TEXT("\n");
	}

	// Save to file
	FFileHelper::SaveStringToFile(LogContent, *ChatLogPath);
}

void DruidsSageChatMemories::EnsureMemoriesDirectoryExists()
{
	FString LogDir = FPaths::GetPath(GetChatLogPath());
	IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();

	if (!PlatformFile.DirectoryExists(*LogDir))
	{
		PlatformFile.CreateDirectoryTree(*LogDir);
	}
}

void DruidsSageChatMemories::TrimMemoriesToMaxEntries(TArray<FChatMemoryEntry>& ChatMemoryEntries)
{
	while (ChatMemoryEntries.Num() > MaxMemoriesEntries)
	{
		ChatMemoryEntries.RemoveAt(0);
	}
}
